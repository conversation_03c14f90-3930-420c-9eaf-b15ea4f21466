import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { files, type = 'pdf' } = body;

    if (!files || !Array.isArray(files) || files.length === 0) {
      return NextResponse.json(
        { error: '缺少文件数据或文件数组为空' },
        { status: 400 }
      );
    }

    console.log('调试HTML生成，文件数量:', files.length);

    // 生成HTML内容（复制前端逻辑）
    let allStyles = new Set<string>();
    let allLinks = new Set<string>();

    const slideContents = files.map((file: any, index: number) => {
      let slideContent = '';

      // 如果是完整的HTML文档，提取内容和样式
      if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
        // 在服务端我们不能使用DOMParser，所以用正则表达式
        const styleMatches = file.content.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
        if (styleMatches) {
          styleMatches.forEach((match: string) => {
            const styleContent = match.replace(/<\/?style[^>]*>/gi, '');
            if (styleContent.trim()) {
              allStyles.add(styleContent);
            }
          });
        }

        const linkMatches = file.content.match(/<link[^>]*rel=["']stylesheet["'][^>]*>/gi);
        if (linkMatches) {
          linkMatches.forEach((match: string) => {
            const hrefMatch = match.match(/href=["']([^"']+)["']/);
            if (hrefMatch && hrefMatch[1]) {
              allLinks.add(hrefMatch[1]);
            }
          });
        }

        // 提取body内容
        const bodyMatch = file.content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        slideContent = bodyMatch ? bodyMatch[1] : file.content;
      } else {
        slideContent = file.content;
      }

      return `
    <div class="slide-page" data-slide="${index + 1}" id="slide-${index + 1}">
      ${slideContent}
    </div>`;
    }).join('\n');

    // 合并所有样式
    const combinedStyles = Array.from(allStyles).join('\n');

    // 生成外部链接标签
    const linkTags = Array.from(allLinks).map(href =>
      `<link href="${href}" rel="stylesheet">`
    ).join('\n    ');

    let htmlContent = '';

    if (type === 'ppt') {
      // PPT HTML结构
      htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${files.length} 页</title>
    ${linkTags}
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f0f0f0;
            scroll-behavior: smooth;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
        }

        /* 原始幻灯片样式 */
        ${combinedStyles}
    </style>
</head>
<body>
    <div class="presentation-container" id="presentation">
        ${slideContents}
    </div>
</body>
</html>`;
    } else {
      // PDF HTML结构
      htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${files.length} 页</title>
    ${linkTags}
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f0f0f0;
            scroll-behavior: smooth;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            page-break-inside: avoid;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page:last-child {
            page-break-after: auto;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
        }

        @media print {
            .slide-page {
                margin: 0;
                box-shadow: none;
                page-break-after: always;
                page-break-inside: avoid;
            }
            .slide-page:last-child {
                page-break-after: auto;
            }
        }

        /* 原始幻灯片样式 */
        ${combinedStyles}
    </style>
</head>
<body>
    <div class="presentation-container">
        ${slideContents}
    </div>
</body>
</html>`;
    }

    console.log('合并HTML创建完成，长度:', htmlContent.length);
    console.log('文件信息:', files.map((f: any) => ({ name: f.name, length: f.content.length })));

    return NextResponse.json({
      html: htmlContent,
      length: htmlContent.length,
      files: files.map((f: any) => ({ name: f.name, length: f.content.length }))
    });

  } catch (error) {
    console.error('HTML调试错误:', error);
    
    return NextResponse.json(
      { 
        error: 'HTML调试失败', 
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
