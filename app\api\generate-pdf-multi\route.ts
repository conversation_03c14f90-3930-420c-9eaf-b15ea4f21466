import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer-core';

// 检查是否有可用的Chrome浏览器
function getChromePath(): string | undefined {
  // Windows常见Chrome路径
  const windowsPaths = [
    'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe',
    process.env.CHROME_PATH,
  ];

  // 尝试找到可用的Chrome路径
  for (const path of windowsPaths) {
    if (path) {
      try {
        const fs = require('fs');
        if (fs.existsSync(path)) {
          return path;
        }
      } catch (error) {
        // 继续尝试下一个路径
      }
    }
  }

  return undefined;
}

// 创建合并的HTML内容
function createCombinedHTML(files: Array<{ name: string; content: string }>): string {
  const slideContents = files.map((file, index) => {
    // 提取body内容
    const bodyMatch = file.content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    const bodyContent = bodyMatch ? bodyMatch[1] : file.content;
    
    return `
      <div class="slide-page" style="
        width: 100vw;
        height: 100vh;
        page-break-after: always;
        page-break-inside: avoid;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        overflow: hidden;
        ${index === files.length - 1 ? 'page-break-after: auto;' : ''}
      ">
        ${bodyContent}
      </div>
    `;
  }).join('');

  // 提取第一个文件的head内容作为基础样式
  const firstFile = files[0];
  const headMatch = firstFile.content.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
  const headContent = headMatch ? headMatch[1] : '';

  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Combined Slides</title>
      ${headContent}
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        html, body {
          width: 100%;
          height: 100%;
          overflow: hidden;
        }
        
        .slide-page {
          background: white;
        }
        
        @media print {
          .slide-page {
            page-break-after: always;
            page-break-inside: avoid;
          }
          
          .slide-page:last-child {
            page-break-after: auto;
          }
        }
      </style>
    </head>
    <body>
      ${slideContents}
    </body>
    </html>
  `;
}

export async function POST(request: NextRequest) {
  let browser = null;
  
  try {
    const body = await request.json();
    const { files, filename = 'combined-slides.pdf', options = {} } = body;

    if (!files || !Array.isArray(files) || files.length === 0) {
      return NextResponse.json(
        { error: '缺少文件数据或文件数组为空' },
        { status: 400 }
      );
    }

    console.log('开始生成多文件PDF，文件数量:', files.length, '文件名:', filename);

    // 获取Chrome路径
    const chromePath = getChromePath();
    if (!chromePath) {
      return NextResponse.json(
        { error: '未找到Chrome浏览器，请确保已安装Chrome' },
        { status: 500 }
      );
    }

    console.log('使用Chrome路径:', chromePath);

    // 创建合并的HTML内容
    const combinedHTML = createCombinedHTML(files);
    console.log('合并HTML创建完成，长度:', combinedHTML.length);

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      executablePath: chromePath,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    console.log('浏览器启动成功');

    const page = await browser.newPage();

    // 设置视口大小
    await page.setViewport({
      width: 1280,
      height: 720,
      deviceScaleFactor: 1
    });

    // 设置HTML内容
    await page.setContent(combinedHTML, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    console.log('HTML内容设置完成，开始生成PDF');

    // 等待字体加载
    await page.evaluate(() => {
      return document.fonts.ready;
    });

    // 使用screen媒体类型以保持网页样式
    await page.emulateMediaType('screen');

    // 生成PDF
    const defaultOptions = {
      format: 'A4' as const,
      landscape: true,
      printBackground: true,
      margin: {
        top: '5mm',
        right: '5mm',
        bottom: '5mm',
        left: '5mm'
      },
      preferCSSPageSize: false,
      displayHeaderFooter: false
    };

    const pdfOptions = { ...defaultOptions, ...options };
    const pdfBuffer = await page.pdf(pdfOptions);

    console.log('PDF生成成功，大小:', pdfBuffer.length, 'bytes');

    // 返回PDF文件
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    });

  } catch (error) {
    console.error('多文件PDF生成错误:', error);
    
    return NextResponse.json(
      { 
        error: '多文件PDF生成失败', 
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  } finally {
    // 确保浏览器被关闭
    if (browser) {
      try {
        await browser.close();
        console.log('浏览器已关闭');
      } catch (error) {
        console.error('关闭浏览器时出错:', error);
      }
    }
  }
}
