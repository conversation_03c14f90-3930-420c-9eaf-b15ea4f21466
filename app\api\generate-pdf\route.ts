import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer-core';

// 检查是否有可用的Chrome浏览器
function getChromePath(): string | undefined {
  // Windows常见Chrome路径
  const windowsPaths = [
    'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe',
    process.env.CHROME_PATH,
  ];

  // 尝试找到可用的Chrome路径
  for (const path of windowsPaths) {
    if (path) {
      try {
        const fs = require('fs');
        if (fs.existsSync(path)) {
          return path;
        }
      } catch (error) {
        // 继续尝试下一个路径
      }
    }
  }

  return undefined;
}

export async function POST(request: NextRequest) {
  let browser = null;
  
  try {
    const body = await request.json();
    const { htmlContent, filename = 'generated.pdf', options = {} } = body;

    if (!htmlContent) {
      return NextResponse.json(
        { error: '缺少HTML内容' },
        { status: 400 }
      );
    }

    console.log('开始生成PDF，文件名:', filename);

    // 获取Chrome路径
    const chromePath = getChromePath();
    if (!chromePath) {
      return NextResponse.json(
        { error: '未找到Chrome浏览器，请确保已安装Chrome' },
        { status: 500 }
      );
    }

    console.log('使用Chrome路径:', chromePath);

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      executablePath: chromePath,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    console.log('浏览器启动成功');

    const page = await browser.newPage();

    // 设置视口大小
    await page.setViewport({
      width: 1280,
      height: 720,
      deviceScaleFactor: 1
    });

    // 设置HTML内容
    await page.setContent(htmlContent, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    console.log('HTML内容设置完成，开始生成PDF');

    // 等待字体加载
    await page.evaluate(() => {
      return document.fonts.ready;
    });

    // 使用screen媒体类型以保持网页样式
    await page.emulateMediaType('screen');

    // 生成PDF
    const defaultOptions = {
      format: 'A4' as const,
      landscape: true,
      printBackground: true,
      margin: {
        top: '10mm',
        right: '10mm',
        bottom: '10mm',
        left: '10mm'
      },
      preferCSSPageSize: false,
      displayHeaderFooter: false
    };

    const pdfOptions = { ...defaultOptions, ...options };
    const pdfBuffer = await page.pdf(pdfOptions);

    console.log('PDF生成成功，大小:', pdfBuffer.length, 'bytes');

    // 返回PDF文件
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    });

  } catch (error) {
    console.error('PDF生成错误:', error);
    
    return NextResponse.json(
      { 
        error: 'PDF生成失败', 
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  } finally {
    // 确保浏览器被关闭
    if (browser) {
      try {
        await browser.close();
        console.log('浏览器已关闭');
      } catch (error) {
        console.error('关闭浏览器时出错:', error);
      }
    }
  }
}
