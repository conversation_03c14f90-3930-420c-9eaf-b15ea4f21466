"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Conversation, GenerationOptions, ModelType, Task, TaskExecutionPhase, FileOperation } from '../types';
import { useAIStore, getAllModelsForProvider } from '@/lib/ai-store'; // 引入getAllModelsForProvider函数

interface ConversationPanelProps {
  conversation: Conversation;
  onSendMessage: (content: string) => void;
  contentType: 'html' | 'markdown';
  setContentType: (type: 'html' | 'markdown') => void;
  styleOptions: {
    style: string;
    complexity: string;
    fileCount: number;
  };
  setStyleOptions: (options: any) => void;
  contextOptions?: {
    maxMessages: number; // 0表示不限制
    keepSystemMessage: boolean;
  };
  setContextOptions?: (options: any) => void;
  onGenerateContent: () => void;
  isGenerating: boolean;
  tasks: Task[];
  executionPhase: TaskExecutionPhase;
  fileOperations: FileOperation[];
  // 流式相关属性
  streamingContent?: string;
  isStreaming?: boolean;
  detectedFiles?: {type: 'html' | 'markdown', filename?: string}[];
  streamingProgress?: string;
  // 任务流式相关属性
  taskStreamingContent?: string;
  isTaskStreaming?: boolean;
  currentStreamingTaskId?: string | null;
}

const ConversationPanel: React.FC<ConversationPanelProps> = ({
  conversation,
  onSendMessage,
  contentType,
  setContentType,
  styleOptions,
  setStyleOptions,
  contextOptions,
  setContextOptions,
  onGenerateContent,
  isGenerating,
  tasks,
  executionPhase,
  fileOperations,
  streamingContent,
  isStreaming,
  detectedFiles,
  streamingProgress,
  taskStreamingContent,
  isTaskStreaming,
  currentStreamingTaskId
}) => {
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // 使用全局AI状态
  const { model, setModel, provider, customModels } = useAIStore();
  const [availableModels, setAvailableModels] = useState<any[]>([]);

  // 获取当前提供商的所有模型
  useEffect(() => {
    const models = getAllModelsForProvider(provider, customModels);
    console.log('🔍 ConversationPanel - 获取当前提供商的所有模型:', {
      provider,
      customModelsCount: customModels.length,
      modelsCount: models.length,
      models: models.map(m => ({ id: m.id, name: m.name }))
    });
    setAvailableModels(models);
  }, [provider, customModels]);

  // 监听自定义模型更新事件
  useEffect(() => {
    const handleCustomModelsUpdated = (event: CustomEvent) => {
      console.log('🔍 ConversationPanel - 收到自定义模型更新事件:', event.detail);
      // 更新可用模型列表
      const models = getAllModelsForProvider(provider, event.detail.customModels);
      setAvailableModels(models);
    };

    // 添加事件监听器
    window.addEventListener('custom-models-updated', handleCustomModelsUpdated as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('custom-models-updated', handleCustomModelsUpdated as EventListener);
    };
  }, [provider]);

  // 将模型转换为select选项格式
  const modelOptions = availableModels.map(model => ({
    value: model.id,
    label: model.name || model.id
  }));

  // 设置面板状态
  const [showSettings, setShowSettings] = useState(false);

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation.messages]);

  // 处理消息发送
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isGenerating) {
      onSendMessage(message);
      setMessage('');
    }
  };

  // 清除对话
  const handleClearConversation = () => {
    // 这里应该调用父组件的方法来清除对话
    // 但为了简单起见，我们只清除输入框
    setMessage('');
  };

  // 处理消息内容，替换代码块为摘要
  const processMessageContent = (content: string) => {
    if (!content) return content;

    // 清理续写标记
    let cleanContent = content.replace(/\[CONTINUE_CONTENT\]/g, '');

    // 匹配代码块模式: ```html{filename=xxx}...```或```markdown{filename=xxx}...```
    const codeBlockRegex = /```(html|markdown|md)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)```/g;

    // 替换所有匹配的代码块为摘要信息
    return cleanContent.replace(codeBlockRegex, (match, type, filename, code) => {
      if (!filename) {
        // 如果没有文件名，保持原样
        return match;
      }

      // 确定文件类型
      const fileType = type === 'md' ? 'markdown' : type;
      const fileTypeLabel = fileType === 'html' ? 'HTML' : 'Markdown';

      // 创建摘要信息
      return `<div class="bg-gray-750 p-3 my-2 rounded-md border border-indigo-800 shadow-md">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-cyan-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span class="font-medium text-cyan-300">文件已保存: ${filename}</span>
        </div>
        <p class="text-sm text-gray-300 mt-1">
          类型: ${fileTypeLabel} | 大小: ~${Math.round(code.length / 100) / 10} KB
        </p>
        <p class="text-xs text-gray-400 mt-1">
          该文件内容已提取并显示在右侧文件预览区域
        </p>
      </div>`;
    });
  };

  return (
    <div className="flex flex-col h-full border-r border-gray-700 bg-gray-800 shadow-md">
      {/* 对话历史区域 */}
      <div className="flex-none bg-gradient-to-r from-indigo-900 via-purple-900 to-indigo-800 border-b border-gray-700 p-4 flex justify-between items-center shadow-md">
        <h2 className="font-semibold text-gray-100 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
          </svg>
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400">对话历史</span>
        </h2>
        <button
          onClick={handleClearConversation}
          className="text-xs px-3 py-1.5 bg-gray-700 hover:bg-gray-600 rounded-full text-cyan-300 transition-colors border border-indigo-800 shadow-md flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-cyan-300" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
            <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
          </svg>
          清除对话
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-4 bg-gradient-to-b from-gray-900 to-gray-800 min-h-0">
        {conversation.messages.length === 0 ? (
          <div className="text-center text-gray-300 mt-8 bg-gray-800 p-6 rounded-xl shadow-lg border border-indigo-900">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-cyan-500 mb-3 glow-effect" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" />
            </svg>
            <p className="font-medium text-cyan-300 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400">开始一个新的对话</p>
            <p className="text-sm mt-2 text-gray-400">例如: "我需要一个产品展示页面，整个结果保存为一个html文件。"</p>
          </div>
        ) : (
          <>
            {conversation.messages
              .filter(msg => msg.role !== 'system')
              .map(message => {
                // 优先用 type 字段判定消息类型
                const msgType = message.type || message.role;
                return (
                  <div
                    key={message.id}
                    className={`mb-5 max-w-[85%] ${msgType === 'user' ? 'ml-auto' : 'mr-auto'} animate-fadeIn`}
                  >
                    <div className="flex items-start">
                      {msgType === 'assistant' ? (
                        <div className="flex-shrink-0 mr-2 mt-1 bg-gradient-to-br from-indigo-600 to-purple-700 rounded-full w-7 h-7 flex items-center justify-center shadow-md glow-effect">
                          <span className="text-xs font-semibold text-white">AI</span>
                        </div>
                      ) : msgType === 'task' ? (
                        <div className="flex-shrink-0 mr-2 mt-1 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-full w-7 h-7 flex items-center justify-center shadow-md glow-effect">
                          <span className="text-xs text-white">⚙️</span>
                        </div>
                      ) : null}
                      <div
                        className={`rounded-2xl px-4 py-3 shadow-md ${
                          msgType === 'user'
                            ? 'bg-gradient-to-r from-indigo-600 to-purple-700 text-white'
                            : msgType === 'task'
                            ? 'bg-gradient-to-r from-gray-700 to-gray-800 text-cyan-300 border border-cyan-900'
                            : 'bg-gray-750 text-gray-100 border border-gray-700'
                        }`}
                        dangerouslySetInnerHTML={{
                          __html: msgType === 'assistant'
                            ? processMessageContent(message.content)
                            : message.content
                        }}
                      />
                      {msgType === 'user' && (
                        <div className="flex-shrink-0 ml-2 mt-1 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full w-7 h-7 flex items-center justify-center shadow-sm">
                          <span className="text-xs font-semibold text-white">你</span>
                        </div>
                      )}
                    </div>
                    <div className={`text-xs text-gray-400 mt-1 ${msgType === 'user' ? 'text-right mr-9' : 'ml-9'}`}>
                      {new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </div>
                  </div>
                );
              })}

            {/* 流式内容显示 */}
            {isStreaming && (
              <div className="mb-5 max-w-[85%] mr-auto animate-fadeIn">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-2 mt-1 bg-gradient-to-br from-indigo-600 to-purple-700 rounded-full w-7 h-7 flex items-center justify-center shadow-md glow-effect">
                    <span className="text-xs font-semibold text-white">AI</span>
                  </div>
                  <div className="rounded-2xl px-4 py-3 bg-gray-750 text-gray-100 border border-gray-700 shadow-md max-w-full">
                    {/* 检测到的文件状态 */}
                    {detectedFiles && detectedFiles.length > 0 && (
                      <div className="mb-3 p-3 bg-blue-900/30 border border-blue-700/50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <div className="animate-pulse h-2 w-2 rounded-full bg-blue-400 mr-2"></div>
                          <span className="text-sm font-medium text-blue-300">检测到文件</span>
                        </div>
                        <div className="space-y-1">
                          {detectedFiles.map((file, index) => (
                            <div key={index} className="flex items-center text-xs text-blue-200">
                              <span className="mr-2">📄</span>
                              <span className="truncate">{file.filename || `${file.type === 'html' ? 'HTML' : 'Markdown'} 文件`}</span>
                              <span className="ml-2 text-blue-400 flex-shrink-0">({file.type.toUpperCase()})</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 流式内容 - 只在没有检测到文件时显示原始内容 */}
                    {streamingContent && !detectedFiles?.length && (
                      <div
                        ref={(el) => {
                          if (el) {
                            // 自动滚动到底部
                            el.scrollTop = el.scrollHeight;
                          }
                        }}
                        className="prose prose-invert max-w-none overflow-hidden"
                        style={{
                          maxHeight: '200px',
                          overflowY: 'auto',
                          wordBreak: 'break-word'
                        }}
                        dangerouslySetInnerHTML={{
                          __html: processMessageContent(streamingContent)
                        }}
                      />
                    )}

                    {/* 进度指示器 */}
                    <div className="flex items-center mt-2 pt-2 border-t border-gray-600">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <span className="ml-2 text-xs text-gray-400 truncate">
                        {streamingProgress || '正在生成...'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 添加loading指示器，当isGenerating为true但不是流式时显示 */}
            {isGenerating && !isStreaming && (
              <div className="mb-5 max-w-[85%] mr-auto animate-fadeIn">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-2 mt-1 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-7 h-7 flex items-center justify-center shadow-sm">
                    <span className="text-xs font-semibold text-white">AI</span>
                  </div>
                  <div className="rounded-2xl px-4 py-3 bg-white text-gray-800 border border-gray-100 shadow-sm">
                    <div className="flex items-center">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <span className="ml-2 text-sm text-gray-600">思考中...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 任务状态区域 */}
      {tasks.length > 0 && (
        <div className="flex-none border-t border-gray-700 p-4 bg-gray-750">
          <h3 className="font-semibold text-gray-200 mb-3 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
            </svg>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400">任务状态</span>
          </h3>
          <div className="text-sm">
            <div className="mb-3 flex items-center bg-gray-800 p-2 rounded-lg border border-gray-700 shadow-md">
              <div className={`w-2 h-2 rounded-full mr-2 ${
                executionPhase === 'planning' ? 'bg-amber-400 glow-effect' :
                executionPhase === 'executing' ? 'bg-cyan-400 glow-effect' :
                executionPhase === 'summarizing' ? 'bg-purple-400 glow-effect' : 'bg-emerald-400 glow-effect'
              }`}></div>
              <span className="font-medium text-gray-300">执行阶段: </span>
              <span className="ml-1 text-gray-200">{
                executionPhase === 'planning' ? '规划中' :
                executionPhase === 'executing' ? '执行中' :
                executionPhase === 'summarizing' ? '总结中' : '已完成'
              }</span>
              {isGenerating && (
                <div className="ml-2 inline-flex items-center">
                  <div className="animate-pulse h-2 w-2 rounded-full bg-cyan-400 mr-1 glow-effect"></div>
                  <div className="animate-pulse h-2 w-2 rounded-full bg-cyan-400 mr-1 glow-effect" style={{ animationDelay: '0.2s' }}></div>
                  <div className="animate-pulse h-2 w-2 rounded-full bg-cyan-400 glow-effect" style={{ animationDelay: '0.4s' }}></div>
                </div>
              )}
            </div>
            <ul className="space-y-2 max-h-40 overflow-y-auto pr-1">
              {tasks.map(task => (
                <li key={task.id} className="flex items-center bg-gray-800 p-2 rounded-lg border border-gray-700 shadow-md transition-all hover:shadow-lg hover:border-indigo-800">
                  <span className={`w-3 h-3 rounded-full mr-2 flex-shrink-0 ${
                    task.status === 'pending' ? 'bg-gray-500' :
                    task.status === 'in-progress' ? 'bg-amber-400 glow-effect' : 'bg-emerald-400 glow-effect'
                  }`}></span>
                  <span className="truncate flex-grow text-gray-200">{task.number}. {task.description}</span>
                  <span className={`ml-2 text-xs px-2 py-1 rounded-full flex-shrink-0 ${
                    task.status === 'pending' ? 'bg-gray-700 text-gray-300 border border-gray-600' :
                    task.status === 'in-progress' ? 'bg-amber-900 text-amber-300 border border-amber-700' : 'bg-emerald-900 text-emerald-300 border border-emerald-700'
                  }`}>
                    {task.status === 'pending' ? '待处理' :
                     task.status === 'in-progress' ? (
                       <span className="flex items-center">
                         进行中
                         {isGenerating && (
                           <span className="ml-1 inline-flex items-center">
                             <span className="animate-pulse h-1 w-1 rounded-full bg-amber-400 mr-0.5 glow-effect"></span>
                             <span className="animate-pulse h-1 w-1 rounded-full bg-amber-400 mr-0.5 glow-effect" style={{ animationDelay: '0.2s' }}></span>
                             <span className="animate-pulse h-1 w-1 rounded-full bg-amber-400 glow-effect" style={{ animationDelay: '0.4s' }}></span>
                           </span>
                         )}
                       </span>
                     ) : '已完成'}
                  </span>
                </li>
              ))}
            </ul>

            {/* 任务流式输出显示 */}
            {isTaskStreaming && taskStreamingContent && currentStreamingTaskId && (
              <div className="mt-4 p-3 bg-gray-750 border border-amber-700/50 rounded-lg">
                <div className="flex items-center mb-2">
                  <div className="animate-pulse h-2 w-2 rounded-full bg-amber-400 mr-2 glow-effect"></div>
                  <span className="text-sm font-medium text-amber-300">
                    任务执行中 - {tasks.find(t => t.id === currentStreamingTaskId)?.description || '未知任务'}
                  </span>
                </div>
                <div
                  ref={(el) => {
                    if (el) {
                      // 自动滚动到底部
                      el.scrollTop = el.scrollHeight;
                    }
                  }}
                  className="bg-gray-800 rounded-md border border-gray-600 p-3 max-h-32 overflow-y-auto"
                >
                  <div className="text-xs text-gray-300 whitespace-pre-wrap font-mono">
                    {taskStreamingContent}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 文件操作状态区域 */}
      {fileOperations.length > 0 && (
        <div className="flex-none border-t border-gray-700 p-4 bg-gray-750">
          <h3 className="font-semibold text-gray-200 mb-3 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" />
            </svg>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400">文件操作</span>
          </h3>
          <ul className="space-y-2 text-sm max-h-40 overflow-y-auto pr-1">
            {fileOperations.map((operation) => (
              <li key={operation.id} className="flex items-center bg-gray-800 p-2 rounded-lg border border-gray-700 shadow-md transition-all hover:shadow-lg hover:border-indigo-800">
                <span className={`w-3 h-3 rounded-full mr-2 flex-shrink-0 ${
                  operation.status === 'pending' ? 'bg-gray-400' :
                  operation.status === 'in-progress' ? 'bg-yellow-400' : 'bg-green-400'
                }`}></span>
                <div className="flex-grow">
                  <div className="flex items-center">
                    <span className="font-medium text-gray-100">
                      {operation.type === 'create' ? '创建文件' :
                       operation.type === 'update' ? '更新文件' : '删除文件'}
                    </span>
                    <span className={`ml-2 text-xs px-1.5 py-0.5 rounded ${
                      operation.fileType === 'html' ? 'bg-indigo-900 text-cyan-300 border border-indigo-700' : 'bg-emerald-900 text-emerald-300 border border-emerald-700'
                    }`}>
                      {operation.fileType}
                    </span>
                  </div>
                  <div className="text-xs text-gray-300 mt-0.5 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" />
                    </svg>
                    {operation.fileName}
                    {operation.taskNumber && <span className="ml-1">- 任务 {operation.taskNumber}</span>}
                  </div>
                </div>
                <span className={`ml-2 text-xs px-2 py-1 rounded-full flex-shrink-0 ${
                  operation.status === 'pending' ? 'bg-gray-100 text-gray-600' :
                  operation.status === 'in-progress' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                }`}>
                  {operation.status === 'pending' ? '待处理' :
                   operation.status === 'in-progress' ? (
                     <span className="flex items-center">
                       进行中
                       {isGenerating && (
                         <span className="ml-1 inline-flex items-center">
                           <span className="animate-pulse h-1 w-1 rounded-full bg-yellow-500 mr-0.5"></span>
                           <span className="animate-pulse h-1 w-1 rounded-full bg-yellow-500 mr-0.5" style={{ animationDelay: '0.2s' }}></span>
                           <span className="animate-pulse h-1 w-1 rounded-full bg-yellow-500" style={{ animationDelay: '0.4s' }}></span>
                         </span>
                       )}
                     </span>
                   ) : '已完成'}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 选项区域 - 移除内容类型选择，只保留模型选择 */}
      <div className="flex-none border-t border-gray-700 p-4 bg-gradient-to-b from-gray-800 to-gray-850">
        {/* 设置按钮 */}
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-semibold text-gray-200 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
            </svg>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400">模型与设置</span>
          </h3>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="text-xs px-3 py-1.5 bg-gray-700 hover:bg-gray-600 rounded-full text-cyan-300 transition-colors border border-indigo-800 shadow-md flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-cyan-300" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
            {showSettings ? '隐藏设置' : '显示设置'}
          </button>
        </div>

        {/* 设置面板 */}
        <div className={`transition-all duration-300 ${showSettings ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
          {/* 模型选择 */}
          <div className="mb-6">
            <label className="text-sm text-gray-300 mb-2 flex items-center">
              选择模型
            </label>
            <div className="relative">
              <select
                className="w-full p-2.5 pr-10 border border-gray-600 rounded-lg bg-gray-700 text-gray-200 shadow-md appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
                value={model}
                onChange={(e) => {
                  console.log('🔍 ConversationPanel - 模型选择变化:', e.target.value);
                  setModel(e.target.value);
                }}
              >
                {modelOptions.map(modelOption => (
                  <option key={modelOption.value} value={modelOption.value}>
                    {modelOption.label}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-cyan-400">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                </svg>
              </div>
            </div>
          </div>

          {/* 上下文窗口配置 */}
          {setContextOptions && contextOptions && (
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-200 mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                </svg>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400">上下文窗口设置</span>
              </h3>

              <div className="mb-4">
                <label className="text-sm font-medium text-gray-300 mb-1 flex">上下文消息数量</label>
                <select
                  value={contextOptions.maxMessages}
                  onChange={e => setContextOptions({...contextOptions, maxMessages: parseInt(e.target.value)})}
                  className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-gray-200 shadow-md focus:ring-cyan-500 focus:border-cyan-500"
                >
                  <option value={0}>全量历史 (不限制)</option>
                  <option value={5}>最近 5 条消息</option>
                  <option value={10}>最近 10 条消息</option>
                  <option value={15}>最近 15 条消息</option>
                  <option value={20}>最近 20 条消息</option>
                  <option value={30}>最近 30 条消息</option>
                </select>
                <p className="text-xs text-gray-400 mt-1">限制发送到API的消息数量，可减少token消耗</p>
              </div>

              <div className="flex items-center mb-4">
                <input
                  type="checkbox"
                  id="keepSystemMessage"
                  checked={contextOptions.keepSystemMessage}
                  onChange={e => setContextOptions({...contextOptions, keepSystemMessage: e.target.checked})}
                  className="h-4 w-4 text-cyan-500 border-gray-600 bg-gray-700 rounded focus:ring-cyan-500"
                />
                <label htmlFor="keepSystemMessage" className="ml-2 inline text-sm text-gray-300">
                  始终保留系统消息
                </label>
              </div>
            </div>
          )}
        </div>

        <div className="text-sm text-gray-300 text-center mt-2 bg-gray-750 p-2 rounded-lg border border-indigo-900 flex items-center justify-center shadow-md">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-cyan-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <span className="text-cyan-300">当AI回复包含代码时，将自动生成内容</span>
        </div>
      </div>

      {/* 消息输入区域 */}
      <div className="flex-none border-t border-gray-700 p-4 bg-gray-800 shadow-inner">
        <form onSubmit={handleSendMessage} className="flex items-center">
          <div className="relative flex-1">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={isGenerating ? "正在生成内容..." : "输入新的提示或修改建议..."}
              className={`w-full py-3 px-4 pl-5 ${isGenerating ? 'bg-gray-800 opacity-75' : 'bg-gray-800'} rounded-full focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all shadow-md text-gray-100 placeholder-gray-400 border border-transparent bg-clip-padding`}
              style={{
                backgroundImage: isGenerating ? 'none' : 'linear-gradient(to right, #1f2937, #1f2937), linear-gradient(to right, #06b6d4, #8b5cf6)',
                backgroundOrigin: 'border-box',
                backgroundClip: 'padding-box, border-box'
              }}
              disabled={isGenerating}
            />
            {isGenerating && (
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-cyan-400 glow-effect"></div>
              </div>
            )}
          </div>
          <button
            type="submit"
            className={`ml-2 p-3 rounded-full shadow-md transition-all duration-200 ${
              message.trim() && !isGenerating
                ? 'bg-gradient-to-r from-cyan-600 to-indigo-700 text-white hover:shadow-lg transform hover:scale-105 hover:from-cyan-500 hover:to-indigo-600 glow-effect'
                : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
            disabled={!message.trim() || isGenerating}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  );
};

export default ConversationPanel;

/* 添加打字指示器样式和动画 */
<style jsx>{`
  .typing-indicator {
    display: inline-flex;
    align-items: center;
  }

  .typing-indicator span {
    height: 8px;
    width: 8px;
    margin: 0 1px;
    background-color: #718096;
    border-radius: 50%;
    display: inline-block;
    animation: typing 1.4s infinite ease-in-out both;
  }

  .typing-indicator span:nth-child(1) {
    animation-delay: 0s;
  }

  .typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing {
    0%, 100% {
      transform: scale(0.6);
      opacity: 0.6;
    }
    50% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }
`}</style>
