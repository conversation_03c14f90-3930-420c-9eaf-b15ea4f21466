/**
 * 从消息中提取HTML代码
 */
export function extractHtmlFromMessage(message: string): { content: string; filename?: string } | null {
  console.log('=== EXTRACTING HTML FROM MESSAGE ===');
  console.log('Message length:', message.length);
  console.log('Message preview:', message.substring(0, 100) + '...');

  // 尝试提取HTML代码块 - 增强正则表达式以处理更多格式变体
  const htmlCodeBlockRegex = /```(?:html|HTML)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)\s*```/;
  const htmlMatch = message.match(htmlCodeBlockRegex);

  // 尝试从标题中提取文件名
  let titleMatch = null;
  if (message.includes('<title>') && message.includes('</title>')) {
    titleMatch = message.match(/<title>([^<]+)<\/title>/);
    console.log('Found title in message:', titleMatch ? titleMatch[1] : 'none');
  }

  // 处理HTML代码块
  if (htmlMatch && htmlMatch[2]) {
    const code = htmlMatch[2].trim();
    let filename = htmlMatch[1] ? htmlMatch[1].trim() : undefined;
    console.log('Found filename in HTML code block tag:', filename);

    // 如果没有从代码块标记中提取到文件名，尝试从代码内容中提取
    if (!filename && code.includes('<title>') && code.includes('</title>')) {
      const codeTitleMatch = code.match(/<title>([^<]+)<\/title>/);
      if (codeTitleMatch && codeTitleMatch[1]) {
        filename = sanitizeFilename(codeTitleMatch[1].trim()) + '.html';
        console.log('Extracted filename from code title tag:', filename);
      }
    }

    // 如果还是没有文件名，尝试使用消息中的标题
    if (!filename && titleMatch && titleMatch[1]) {
      filename = sanitizeFilename(titleMatch[1].trim()) + '.html';
      console.log('Using message title as filename:', filename);
    }

    if (filename) {
      console.log('Found filename for HTML:', filename);
    }
    console.log('FOUND HTML CODE BLOCK!');
    console.log('HTML code block length:', code.length);
    console.log('HTML code block preview:', code.substring(0, 100) + '...');

    // 检查是否是完整HTML
    if (code.includes('<!DOCTYPE html>') || code.includes('<html')) {
      console.log('Code is a complete HTML document');
      return { content: code, filename: filename || 'index.html' };
    } else {
      console.log('Code is not a complete HTML document, wrapping it');
      // 如果不是完整HTML，包装它
      const wrappedCode = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${filename ? filename.replace('.html', '') : 'Hello World'}</title>
</head>
<body>
  ${code}
</body>
</html>`;
      console.log('Wrapped HTML code length:', wrappedCode.length);
      return { content: wrappedCode, filename: filename || 'index.html' };
    }
  }

  // 尝试匹配HTML标签块（不在代码块内但包含HTML标签的内容）
  const htmlTagRegex = /<([a-z][a-z0-9]*)\b[^>]*>[\s\S]*?<\/\1>/i;
  if (htmlTagRegex.test(message) && !message.includes('```')) {
    console.log('Found HTML tags outside of code blocks, attempting extraction');
    // 处理可能包含HTML但不在代码块中的情况
    let content = message;
    // 如果内容不是完整的HTML文档，则包装它
    if (!content.includes('<!DOCTYPE html>') && !content.includes('<html')) {
      content = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Extracted Content</title>
</head>
<body>
  ${content}
</body>
</html>`;
    }
    return { content, filename: 'index.html' };
  }

  // 直接尝试提取完整HTML文档
  if (message.includes('<!DOCTYPE html>') && message.includes('</html>')) {
    console.log('FOUND COMPLETE HTML DOCUMENT!');
    const startIndex = message.indexOf('<!DOCTYPE html>');
    const endIndex = message.indexOf('</html>', startIndex) + 7; // +7 to include '</html>'

    if (startIndex >= 0 && endIndex > startIndex) {
      const html = message.substring(startIndex, endIndex);
      console.log('Extracted complete HTML document directly, length:', html.length);
      console.log('HTML document preview:', html.substring(0, 100) + '...');
      // 尝试从 HTML 中提取标题作为文件名
      let filename;
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        filename = sanitizeFilename(titleMatch[1].trim()) + '.html';
        console.log('Extracted filename from title:', filename);
      }

      return { content: html, filename: filename || 'index.html' };
    }
  }

  // 尝试提取HTML片段（没有完整的HTML文档结构但包含HTML标签）
  const htmlFragmentRegex = /<([a-z][a-z0-9]*)\b[^>]*>[\s\S]*?<\/\1>/i;
  const fragmentMatch = message.match(htmlFragmentRegex);
  if (fragmentMatch) {
    console.log('Found HTML fragment');
    const fragment = message;
    const wrappedFragment = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HTML Fragment</title>
</head>
<body>
  ${fragment}
</body>
</html>`;
    return { content: wrappedFragment, filename: 'fragment.html' };
  }

  console.log('NO HTML CODE FOUND!');
  return null;
}

/**
 * 清理文件名，移除不安全的字符
 */
function sanitizeFilename(filename: string): string {
  // 移除不安全的文件名字符，替换为连字符
  return filename
    .replace(/[/\\?%*:|"<>]/g, '-')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * 从消息中提取Markdown代码
 */
export function extractMarkdownFromMessage(message: string): { content: string; filename?: string } | null {
  console.log('=== EXTRACTING MARKDOWN FROM MESSAGE ===');
  console.log('Message length:', message.length);
  console.log('Message preview:', message.substring(0, 100) + '...');

  // 尝试提取Markdown代码块 - 增强正则表达式以处理更多格式变体
  const markdownCodeBlockRegex = /```(?:markdown|Markdown|md|MD)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)\s*```/;
  const mdMatch = message.match(markdownCodeBlockRegex);

  if (mdMatch && mdMatch[2]) {
    const code = mdMatch[2].trim();
    let filename = mdMatch[1] ? sanitizeFilename(mdMatch[1].trim()) : undefined;
    console.log('FOUND MARKDOWN CODE BLOCK!');
    console.log('Markdown code block length:', code.length);
    console.log('Markdown code preview:', code.substring(0, 100) + '...');

    if (filename) {
      // 确保文件名有正确的扩展名
      if (!filename.endsWith('.md')) {
        filename = filename + '.md';
      }
      console.log('Found filename in markdown code block:', filename);
    }

    // 尝试从第一行标题提取文件名
    if (!filename && code.startsWith('#')) {
      const titleMatch = code.match(/^#\s+([^\n]+)/);
      if (titleMatch && titleMatch[1]) {
        const title = titleMatch[1].trim();
        const mdFilename = sanitizeFilename(title) + '.md';
        console.log('Generated filename from markdown title:', mdFilename);
        return { content: code, filename: mdFilename };
      }
    }

    return { content: code, filename: filename || 'index.md' };
  }

  // 尝试匹配常见的Markdown格式（如标题、列表、表格等）
  const markdownPatterns = [
    /^#+\s+.+$/m,                 // 标题
    /^\s*[-*+]\s+.+$/m,           // 无序列表
    /^\s*\d+\.\s+.+$/m,           // 有序列表
    /^\s*>\s+.+$/m,               // 引用
    /^\s*```[\s\S]*?```\s*$/m,    // 代码块
    /^\|.+\|\s*$/m,               // 表格
    /^\s*\*\*[^*]+\*\*\s*$/m,     // 粗体
    /^\s*\*[^*]+\*\s*$/m,         // 斜体
    /^\s*\[.+\]\(.+\)\s*$/m,      // 链接
    /^\s*!\[.+\]\(.+\)\s*$/m,     // 图片
  ];

  // 检查消息是否包含多个Markdown模式
  let markdownPatternCount = 0;
  for (const pattern of markdownPatterns) {
    if (pattern.test(message)) {
      markdownPatternCount++;
      if (markdownPatternCount >= 2) {
        // 如果匹配到至少两种Markdown模式，认为这是Markdown内容
        console.log('Found multiple Markdown patterns in message');

        // 尝试从第一个标题生成文件名
        const titleMatch = message.match(/^#+\s+(.+)$/m);
        let filename = 'document.md';

        if (titleMatch && titleMatch[1]) {
          filename = sanitizeFilename(titleMatch[1].trim()) + '.md';
          console.log('Generated filename from first heading:', filename);
        }

        return { content: message, filename };
      }
    }
  }

  // 如果没有找到代码块，尝试提取以#开头的内容
  if (message.includes('# ')) {
    console.log('Message contains # heading, extracting Markdown content');
    const lines = message.split('\n');
    const titleLineIndex = lines.findIndex(line => line.trim().startsWith('# '));

    if (titleLineIndex >= 0) {
      const markdown = lines.slice(titleLineIndex).join('\n');
      console.log('EXTRACTED MARKDOWN CONTENT!');
      console.log('Markdown content length:', markdown.length);
      console.log('Markdown content preview:', markdown.substring(0, 100) + '...');

      // 尝试从第一行提取文件名
      const firstLine = lines[titleLineIndex];
      const filenameMatch = firstLine.match(/# (.+?)(?:\s*\((.+?)\))?$/i);
      let filename = filenameMatch ? filenameMatch[2] || filenameMatch[1] : undefined;

      // 如果没有从标题中提取到文件名，则生成一个基于标题的文件名
      if (!filename && filenameMatch && filenameMatch[1]) {
        const title = filenameMatch[1].trim();
        filename = sanitizeFilename(title) + '.md';
        console.log('Generated filename from markdown heading:', filename);
      } else if (filename) {
        // 确保文件名有正确的扩展名
        if (!filename.endsWith('.md')) {
          filename = filename + '.md';
        }
        filename = sanitizeFilename(filename);
        console.log('Found filename in markdown heading:', filename);
      }

      return { content: markdown, filename: filename || 'index.md' };
    }
  }

  // 尝试检测纯文本内容是否可能是Markdown
  if (!message.includes('```') && !message.includes('<!DOCTYPE') && message.length > 50) {
    // 检查是否包含多行文本和基本格式
    const lines = message.split('\n').filter(line => line.trim().length > 0);
    if (lines.length >= 3) {
      // 检查是否有基本的Markdown格式特征
      const hasFormatting = lines.some(line =>
        /^#+\s/.test(line) || // 标题
        /^[-*+]\s/.test(line) || // 列表
        /^>\s/.test(line) || // 引用
        /\*\*.*\*\*/.test(line) || // 粗体
        /\*[^*]+\*/.test(line) || // 斜体
        /\[.*\]\(.*\)/.test(line) // 链接
      );

      if (hasFormatting) {
        console.log('Detected plain text with Markdown formatting');
        return { content: message, filename: 'content.md' };
      }
    }
  }

  console.log('NO MARKDOWN CODE FOUND!');
  return null;
}

/**
 * 从消息中提取代码
 */
export function extractCodeFromMessage(message: string, contentType: 'html' | 'markdown'): { content: string; filename?: string } | null {
  console.log('=== EXTRACTING CODE FROM MESSAGE ===');
  console.log('Content type:', contentType);
  console.log('Message length:', message.length);

  // 先尝试使用指定的内容类型提取
  const result = contentType === 'html'
    ? extractHtmlFromMessage(message)
    : extractMarkdownFromMessage(message);

  if (result) {
    return result;
  }

  // 如果指定类型提取失败，尝试另一种类型
  console.log(`Extraction failed with ${contentType}, trying alternate content type`);
  const alternateResult = contentType === 'html'
    ? extractMarkdownFromMessage(message)
    : extractHtmlFromMessage(message);

  if (alternateResult) {
    console.log(`Successfully extracted content using alternate type: ${contentType === 'html' ? 'markdown' : 'html'}`);
  }

  return alternateResult;
}

/**
 * 从消息中提取多个文件
 * @param message 消息内容
 * @param messageId 消息 ID，用于跟踪文件来源
 */
export function extractMultipleFilesFromMessage(
  message: string,
  messageId: string
): Array<{ content: string; filename?: string; contentType: 'html' | 'markdown'; messageId: string; isNew?: boolean }> {
  console.log('=== EXTRACTING MULTIPLE FILES FROM MESSAGE ===');
  const files: Array<{ content: string; filename?: string; contentType: 'html' | 'markdown'; messageId: string; isNew?: boolean }> = [];

  // 清理续写标记
  const cleanMessage = message.replace(/\[CONTINUE_CONTENT\]/g, '');

  console.log('Message length:', cleanMessage.length);
  console.log('Message preview:', cleanMessage.substring(0, 200) + '...');

  // 首先检查是否存在嵌套的代码块
  const hasNestedCodeBlocks = checkForNestedCodeBlocks(cleanMessage);
  if (hasNestedCodeBlocks) {
    console.log('Detected nested code blocks, using specialized extraction logic');
    const extractedFiles = extractNestedCodeBlocks(cleanMessage, messageId);
    if (extractedFiles.length > 0) {
      return extractedFiles;
    }
  }

  // 使用更宽松的正则表达式匹配代码块 - 增强兼容性
  const codeBlockRegex = /```(?:html|HTML|markdown|Markdown|md|MD)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)```/g;
  let match;

  // 遍历所有匹配的代码块
  while ((match = codeBlockRegex.exec(cleanMessage)) !== null) {
    const [fullMatch, rawFilename, content] = match;
    // 从完整匹配中提取块类型
    const blockTypeMatch = fullMatch.match(/```(html|HTML|markdown|Markdown|md|MD)/);
    const blockType = blockTypeMatch ? blockTypeMatch[1].toLowerCase() : '';

    console.log('找到代码块:', { blockType, rawFilename, contentLength: content?.length });

    if (!content?.trim()) {
      console.log('跳过空内容的代码块');
      continue;
    }

    const contentType = (blockType === 'html' || blockType === 'HTML') ? 'html' : 'markdown';

    // 处理文件名
    let filename = rawFilename?.trim();

    // 如果没有指定文件名，尝试从内容中提取
    if (!filename) {
      if (contentType === 'html' && content.includes('<title>')) {
        const titleMatch = content.match(/<title>([^<]+)<\/title>/);
        if (titleMatch && titleMatch[1]) {
          filename = sanitizeFilename(titleMatch[1].trim()) + '.html';
          console.log('Extracted filename from HTML title tag:', filename);
        }
      } else if (contentType === 'markdown' && content.startsWith('#')) {
        const titleMatch = content.match(/^#\s+([^\n]+)/);
        if (titleMatch && titleMatch[1]) {
          filename = sanitizeFilename(titleMatch[1].trim()) + '.md';
          console.log('Extracted filename from Markdown heading:', filename);
        }
      }
    }

    // 如果还是没有文件名，生成默认文件名
    if (!filename) {
      filename = `file${files.length + 1}.${contentType === 'html' ? 'html' : 'md'}`;
    }

    // 确保文件名有正确的扩展名
    if (contentType === 'html' && !filename.toLowerCase().endsWith('.html')) {
      filename = filename + '.html';
    } else if (contentType === 'markdown' && !filename.toLowerCase().endsWith('.md')) {
      filename = filename + '.md';
    }

    // 确保文件名唯一
    let counter = 1;
    let originalName = filename;
    while (files.some(f => f.filename === filename)) {
      const ext = contentType === 'html' ? '.html' : '.md';
      const base = originalName.replace(/\.[^.]+$/, '');
      filename = `${base}-${counter}${ext}`;
      counter++;
    }

    console.log(`找到 ${contentType} 文件: ${filename}`);
    files.push({
      content: content.trim(),
      filename,
      contentType,
      messageId,
      isNew: true
    });
  }

  console.log(`Found ${files.length} files using regex`);

  // 尝试提取非代码块形式的HTML文档
  if (files.length === 0 && cleanMessage.includes('<!DOCTYPE html>') && cleanMessage.includes('</html>')) {
    console.log('Attempting to extract complete HTML document');
    const startIndex = cleanMessage.indexOf('<!DOCTYPE html>');
    const endIndex = cleanMessage.indexOf('</html>', startIndex) + 7; // +7 to include '</html>'

    if (startIndex >= 0 && endIndex > startIndex) {
      const html = cleanMessage.substring(startIndex, endIndex);
      let filename = 'index.html';

      // 尝试从 HTML 中提取标题作为文件名
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        filename = sanitizeFilename(titleMatch[1].trim()) + '.html';
      }

      files.push({
        content: html,
        filename,
        contentType: 'html',
        messageId,
        isNew: true
      });
      console.log(`Extracted complete HTML document directly, length: ${html.length}`);
    }
  }

  // 如果仍然没有找到文件，尝试将整个消息作为一个文件
  if (files.length === 0) {
    // 先尝试HTML提取
    const htmlResult = extractHtmlFromMessage(cleanMessage);
    if (htmlResult) {
      files.push({
        ...htmlResult,
        contentType: 'html',
        messageId,
        isNew: true
      });
      console.log(`Extracted entire message as HTML, length: ${htmlResult.content.length}`);
    } else {
      // 如果HTML提取失败，尝试Markdown提取
      const mdResult = extractMarkdownFromMessage(cleanMessage);
      if (mdResult) {
        files.push({
          ...mdResult,
          contentType: 'markdown',
          messageId,
          isNew: true
        });
        console.log(`Extracted entire message as Markdown, length: ${mdResult.content.length}`);
      }
    }
  }

  // 如果仍然没有文件，尝试分析消息结构并提取多个文件
  if (files.length === 0 && cleanMessage.length > 500) {
    console.log('Attempting to analyze message structure for multiple files');

    // 尝试查找文件名模式，如“文件名.html”或“文件名.md”
    const filenamePatterns = [
      /([\w-]+\.html)\s*[:：]/gi,  // HTML文件名模式
      /([\w-]+\.md)\s*[:：]/gi,    // Markdown文件名模式
      /["']([\w-]+\.(?:html|md))["']/gi, // 引号中的文件名
      /\b(index\.(?:html|md))\b/gi, // 常见索引文件
    ];

    const potentialFiles: {name: string, type: 'html' | 'markdown', startIndex: number}[] = [];

    // 从消息中提取可能的文件名
    for (const pattern of filenamePatterns) {
      let fileMatch;
      while ((fileMatch = pattern.exec(cleanMessage)) !== null) {
        const filename = fileMatch[1];
        const contentType = filename.toLowerCase().endsWith('.html') ? 'html' : 'markdown';
        potentialFiles.push({
          name: filename,
          type: contentType,
          startIndex: fileMatch.index
        });
      }
    }

    // 按在消息中的位置排序
    potentialFiles.sort((a, b) => a.startIndex - b.startIndex);

    // 如果找到多个文件名，尝试分割消息
    if (potentialFiles.length > 1) {
      console.log(`Found ${potentialFiles.length} potential files in message`);

      for (let i = 0; i < potentialFiles.length; i++) {
        const currentFile = potentialFiles[i];
        const nextFile = potentialFiles[i + 1];

        // 确定当前文件的内容范围
        const startPos = cleanMessage.indexOf(':', currentFile.startIndex) + 1;
        const endPos = nextFile ? nextFile.startIndex : cleanMessage.length;

        if (startPos > 0 && endPos > startPos) {
          let content = cleanMessage.substring(startPos, endPos).trim();

          // 如果是HTML内容但不是完整的HTML文档，包装它
          if (currentFile.type === 'html' && !content.includes('<!DOCTYPE html>')) {
            content = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${currentFile.name.replace('.html', '')}</title>
</head>
<body>
  ${content}
</body>
</html>`;
          }

          files.push({
            content,
            filename: currentFile.name,
            contentType: currentFile.type,
            messageId,
            isNew: true
          });

          console.log(`Extracted ${currentFile.type} file: ${currentFile.name}, length: ${content.length}`);
        }
      }
    }
  }

  // 如果仍然没有提取到文件，创建一个默认文件
  if (files.length === 0 && cleanMessage.length > 0) {
    console.log('Creating default file from message content');

    // 判断消息是否可能是HTML或Markdown
    const isLikelyHTML = cleanMessage.includes('<') && cleanMessage.includes('>') &&
                        (cleanMessage.includes('<div') || cleanMessage.includes('<p') || cleanMessage.includes('<h'));
    const isLikelyMarkdown = cleanMessage.includes('#') || cleanMessage.includes('- ') ||
                           cleanMessage.includes('*') || cleanMessage.includes('[') && cleanMessage.includes('](');

    const contentType = isLikelyHTML ? 'html' : 'markdown';
    let filename = contentType === 'html' ? 'index.html' : 'document.md';
    let content = cleanMessage;

    // 如果是HTML内容但不是完整的HTML文档，包装它
    if (contentType === 'html' && !content.includes('<!DOCTYPE html>')) {
      content = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated Content</title>
</head>
<body>
  ${content}
</body>
</html>`;
    }

    files.push({
      content,
      filename,
      contentType,
      messageId,
      isNew: true
    });
  }

  console.log(`Total files extracted: ${files.length}`);
  return files;
}

/**
 * 检查消息中是否存在嵌套的代码块
 * @param message 消息内容
 */
function checkForNestedCodeBlocks(message: string): boolean {
  // 检查是否有嵌套的代码块标记
  const codeBlockStartCount = (message.match(/```/g) || []).length;

  // 如果有奇数个```，说明代码块没有正确闭合
  if (codeBlockStartCount % 2 !== 0) {
    console.log('Detected unclosed code blocks');
    return true;
  }

  // 检查是否有代码块外的描述性文本
  const codeBlockRegex = /```(?:html|HTML|markdown|Markdown|md|MD)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)```/g;
  let match;
  let lastIndex = 0;
  let hasTextOutsideCodeBlocks = false;

  while ((match = codeBlockRegex.exec(message)) !== null) {
    // 检查代码块前是否有文本
    if (match.index > lastIndex && message.substring(lastIndex, match.index).trim().length > 0) {
      console.log('Detected text before code block:', message.substring(lastIndex, match.index).trim());
      hasTextOutsideCodeBlocks = true;
    }

    lastIndex = match.index + match[0].length;
  }

  // 检查最后一个代码块后是否有文本
  if (lastIndex < message.length && message.substring(lastIndex).trim().length > 0) {
    console.log('Detected text after code blocks:', message.substring(lastIndex).trim());
    hasTextOutsideCodeBlocks = true;
  }

  // 检查代码块内部是否包含```
  const codeContents: string[] = [];
  message.replace(/```(?:html|HTML|markdown|Markdown|md|MD)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)```/g, (_, __, content) => {
    codeContents.push(content);
    return '';
  });

  const hasNestedBackticks = codeContents.some(content => content.includes('```'));
  if (hasNestedBackticks) {
    console.log('Detected nested backticks inside code blocks');
    return true;
  }

  return hasTextOutsideCodeBlocks || hasNestedBackticks;
}

/**
 * 从消息中提取嵌套的代码块
 * @param message 消息内容
 * @param messageId 消息 ID
 */
function extractNestedCodeBlocks(
  message: string,
  messageId: string
): Array<{ content: string; filename?: string; contentType: 'html' | 'markdown'; messageId: string; isNew?: boolean }> {
  console.log('=== EXTRACTING NESTED CODE BLOCKS ===');
  const files: Array<{ content: string; filename?: string; contentType: 'html' | 'markdown'; messageId: string; isNew?: boolean }> = [];

  // 处理HTML代码块
  const htmlRegex = /```(?:html|HTML)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)```/g;
  let htmlMatch;

  while ((htmlMatch = htmlRegex.exec(message)) !== null) {
    const [fullMatch, rawFilename, content] = htmlMatch;

    if (!content?.trim()) {
      console.log('Skipping empty HTML code block');
      continue;
    }

    let filename = rawFilename?.trim();

    // 如果没有指定文件名，尝试从内容中提取
    if (!filename && content.includes('<title>')) {
      const titleMatch = content.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        filename = sanitizeFilename(titleMatch[1].trim()) + '.html';
        console.log('Extracted filename from HTML title tag:', filename);
      }
    }

    // 如果仍然没有文件名，生成一个默认文件名
    if (!filename) {
      filename = generateDefaultFileName('html', files.length);
      console.log('Generated default HTML filename:', filename);
    }

    // 检查是否是完整HTML
    const isCompleteHtml = content.includes('<!DOCTYPE html>') || content.includes('<html');
    const finalContent = isCompleteHtml ? content : `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${filename.replace('.html', '')}</title>
</head>
<body>
  ${content}
</body>
</html>`;

    files.push({
      content: finalContent,
      filename,
      contentType: 'html',
      messageId,
      isNew: true
    });

    console.log(`Extracted HTML code block, length: ${finalContent.length}`);
  }

  // 处理Markdown代码块
  const mdRegex = /```(?:markdown|Markdown|md|MD)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?\s*([\s\S]*?)```/g;
  let mdMatch;

  while ((mdMatch = mdRegex.exec(message)) !== null) {
    const [fullMatch, rawFilename, content] = mdMatch;

    if (!content?.trim()) {
      console.log('Skipping empty Markdown code block');
      continue;
    }

    let filename = rawFilename?.trim();

    // 如果没有指定文件名，尝试从内容中提取
    if (!filename && content.startsWith('#')) {
      const titleMatch = content.match(/^#\s+([^\n]+)/);
      if (titleMatch && titleMatch[1]) {
        filename = sanitizeFilename(titleMatch[1].trim()) + '.md';
        console.log('Extracted filename from Markdown heading:', filename);
      }
    }

    // 如果仍然没有文件名，生成一个默认文件名
    if (!filename) {
      filename = generateDefaultFileName('markdown', files.length);
      console.log('Generated default Markdown filename:', filename);
    }

    files.push({
      content,
      filename,
      contentType: 'markdown',
      messageId,
      isNew: true
    });

    console.log(`Extracted Markdown code block, length: ${content.length}`);
  }

  // 如果没有找到任何代码块，尝试直接提取HTML内容
  if (files.length === 0 && message.includes('<!DOCTYPE html>')) {
    const startIndex = message.indexOf('<!DOCTYPE html>');
    const endIndex = message.indexOf('</html>', startIndex) + 7; // +7 to include '</html>'

    if (startIndex >= 0 && endIndex > startIndex) {
      const html = message.substring(startIndex, endIndex);
      let filename;

      // 尝试从HTML中提取标题作为文件名
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        filename = sanitizeFilename(titleMatch[1].trim()) + '.html';
      }

      files.push({
        content: html,
        filename,
        contentType: 'html',
        messageId,
        isNew: true
      });

      console.log(`Extracted complete HTML document directly, length: ${html.length}`);
    }
  }

  // 如果仍然没有找到任何文件，尝试将整个消息处理为一个文件
  if (files.length === 0) {
    // 检查消息是否包含HTML标记
    if (/<([a-z][a-z0-9]*)\b[^>]*>[\s\S]*?<\/\1>/i.test(message)) {
      // 移除消息前面的描述文本，只保留代码部分
      const htmlFragmentRegex = /<([a-z][a-z0-9]*)\b[^>]*>[\s\S]*?<\/\1>/i;
      const fragmentMatch = message.match(htmlFragmentRegex);

      if (fragmentMatch) {
        const startIndex = message.indexOf(fragmentMatch[0]);
        const content = message.substring(startIndex);
        const isCompleteHtml = content.includes('<!DOCTYPE html>') || content.includes('<html');

        // 尝试从代码块中提取文件名
        let filename = 'index.html';
        const filenameMatch = message.match(/```(?:html|HTML)(?:\s*\{\s*filename\s*=\s*([^}]+)\s*\})?/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].trim();
          console.log('Extracted filename from code block tag:', filename);
        }

        const finalContent = isCompleteHtml ? content : `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${filename.replace('.html', '')}</title>
</head>
<body>
  ${content}
</body>
</html>`;

        files.push({
          content: finalContent,
          filename,
          contentType: 'html',
          messageId,
          isNew: true
        });

        console.log(`Extracted HTML content from message, length: ${finalContent.length}`);
      }
    } else {
      // 否则作为Markdown处理
      files.push({
        content: message,
        filename: 'document.md',
        contentType: 'markdown',
        messageId,
        isNew: true
      });

      console.log(`Extracted entire message as Markdown, length: ${message.length}`);
    }
  }

  return files;
}

/**
 * 生成默认文件名
 */
export function generateDefaultFileName(contentType: 'html' | 'markdown', index: number = 0): string {
  const timestamp = new Date().getTime();
  const suffix = index > 0 ? `-${index}` : '';
  return contentType === 'html'
    ? `index-${timestamp}${suffix}.html`
    : `document-${timestamp}${suffix}.md`;
}
