# 幻灯片导出功能使用指南

## 功能概述

新增的导出功能允许用户将生成的多页 slide 的 HTML 文件一键导出为 PDF 或 PPT 格式，支持保持原有排版和样式。

## 功能特点

- **自动检测**：自动识别包含 `<div class="slide"` 或 `class="slide"` 的 HTML 文件
- **多格式支持**：支持导出为 PDF 和 PPT 格式
- **样式保持**：导出时保持原有的排版和样式
- **批量处理**：支持多个幻灯片文件的批量导出

## 使用方法

### 1. 生成幻灯片内容

首先，在内容生成器中生成包含幻灯片的 HTML 文件。例如：

```
请帮我制作一个关于人工智能的PPT，包含以下内容：
1. 封面页
2. AI定义
3. 核心技术
4. 应用场景
5. 未来展望
```

### 2. 查看导出按钮

当系统检测到生成的文件中包含幻灯片格式的 HTML 文件时，会在右侧内容预览面板的顶部工具栏中显示"导出"按钮（紫色按钮，位于"下载全部文件"按钮旁边）。

### 3. 选择导出格式

点击"导出"按钮后，会弹出导出选择对话框，显示：
- 检测到的幻灯片文件列表
- 两种导出格式选项：
  - **导出为 PDF**：生成高质量的 PDF 文件，保持原有样式
  - **导出为 PPT 格式**：生成 PowerPoint 兼容的 HTML 文件

### 4. 完成导出

选择导出格式后，系统会自动处理并下载文件：
- **PDF 导出**：单个文件直接导出，多个文件分别导出
- **PPT 导出**：生成包含所有幻灯片的 HTML 文件，支持打印和演示

## 技术实现

### 幻灯片检测

系统通过以下方式检测幻灯片内容：

```typescript
const isSlideContent = (content: string): boolean => {
  return content.includes('<div class="slide"') || content.includes('class="slide');
};
```

### PDF 导出

使用 `html2pdf.js` 库实现：
- 支持自定义页面尺寸（1280x720，16:9 比例）
- 高质量图像输出（JPEG，98% 质量）
- 横向布局，适合幻灯片展示

### PPT 导出

生成 PowerPoint 兼容的 HTML 文件：
- 包含所有幻灯片内容
- 支持键盘导航（方向键翻页）
- 支持打印功能
- 响应式设计

## 注意事项

1. **文件格式要求**：只有包含 `class="slide"` 的 HTML 文件才会被识别为幻灯片
2. **浏览器兼容性**：PDF 导出功能需要现代浏览器支持
3. **文件大小**：复杂的幻灯片可能导致较大的 PDF 文件
4. **样式依赖**：确保幻灯片的 CSS 样式完整，以保证导出效果

## 故障排除

### 导出按钮不显示
- 检查生成的 HTML 文件是否包含 `class="slide"` 
- 确保文件状态为"已完成"

### PDF 导出失败
- 检查浏览器控制台是否有错误信息
- 尝试刷新页面后重新导出
- 确保浏览器允许下载文件

### 样式丢失
- 检查 HTML 文件中的 CSS 样式是否完整
- 确保使用内联样式或完整的 `<style>` 标签

## 示例

### 标准幻灯片结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>幻灯片标题</title>
    <style>
        .slide {
            width: 1280px;
            min-height: 720px;
            /* 其他样式 */
        }
    </style>
</head>
<body>
    <div class="slide">
        <!-- 幻灯片内容 -->
    </div>
</body>
</html>
```

### 多页幻灯片

系统支持检测和导出多个独立的幻灯片文件，每个文件包含一页内容。

## 更新日志

- **v1.0.0**：初始版本，支持 PDF 和 PPT 格式导出
- 支持自动检测幻灯片内容
- 支持批量导出多个文件
- 保持原有样式和排版
