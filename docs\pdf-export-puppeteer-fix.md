# PDF导出功能修复 - 使用Puppeteer替代html2pdf

## 问题描述

原有的PDF导出功能使用 `html2pdf.js` 库，该库基于 `html2canvas` 和 `jsPDF`，在处理复杂的HTML和CSS时经常出现渲染问题，导致生成空白页面。

## 解决方案

采用 **Puppeteer** 作为PDF生成引擎，通过真实的Chrome浏览器引擎来渲染HTML，确保PDF输出与网页的视觉效果完全一致。

## 实施步骤

### 1. 安装依赖

```bash
npm install puppeteer-core
```

注意：使用 `puppeteer-core` 而不是 `puppeteer`，因为后者会自动下载Chrome，在某些网络环境下可能失败。

### 2. 创建API路由

#### 单文件PDF生成 - `/api/generate-pdf/route.ts`

- 支持单个HTML文件转PDF
- 自动检测系统Chrome路径
- 使用Puppeteer的 `page.pdf()` 方法
- 支持自定义PDF选项（格式、边距等）

#### 多文件PDF生成 - `/api/generate-pdf-multi/route.ts`

- 支持多个HTML文件合并为一个PDF
- 自动分页处理
- 保持每个文件的样式和布局
- 优化的HTML合并算法

### 3. 修改前端导出逻辑

#### 更新 `export-button.tsx`

- 将原有的 `html2pdf` 调用替换为API调用
- 支持单文件和多文件导出
- 改进的错误处理和用户反馈
- 保持原有的UI和交互逻辑

### 4. Chrome浏览器检测

API路由会自动检测以下Chrome路径：
- `C:\Program Files\Google\Chrome\Application\chrome.exe`
- `C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`
- `C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe`
- 环境变量 `CHROME_PATH`

## 技术特性

### PDF生成配置

```javascript
{
  format: 'A4',
  landscape: true,
  printBackground: true,
  margin: {
    top: '5mm',
    right: '5mm',
    bottom: '5mm',
    left: '5mm'
  }
}
```

### 浏览器启动参数

```javascript
{
  headless: true,
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-accelerated-2d-canvas',
    '--no-first-run',
    '--no-zygote',
    '--single-process',
    '--disable-gpu'
  ]
}
```

### 视口设置

- 宽度：1280px
- 高度：720px
- 设备缩放因子：1
- 媒体类型：screen（保持网页样式）

## 优势对比

| 特性 | html2pdf.js | Puppeteer |
|------|-------------|-----------|
| 渲染引擎 | html2canvas + jsPDF | Chrome浏览器 |
| 样式支持 | 有限 | 完整 |
| JavaScript支持 | 无 | 完整 |
| 字体渲染 | 有问题 | 完美 |
| 复杂布局 | 经常失败 | 完美支持 |
| 渐变背景 | 不支持 | 完美支持 |
| 响应式设计 | 有限 | 完整 |

## 使用方法

### 单文件导出

```javascript
const response = await fetch('/api/generate-pdf', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    htmlContent: file.content,
    filename: 'example.pdf',
    options: { /* PDF选项 */ }
  })
});
```

### 多文件导出

```javascript
const response = await fetch('/api/generate-pdf-multi', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    files: [{ name: 'file1.html', content: '...' }],
    filename: 'combined.pdf',
    options: { /* PDF选项 */ }
  })
});
```

## 测试验证

1. 访问内容生成器页面
2. 生成包含复杂样式的HTML幻灯片
3. 点击"导出"按钮选择PDF格式
4. 验证生成的PDF是否完整保留了原始样式

## 注意事项

1. **Chrome依赖**：服务器必须安装Chrome浏览器
2. **内存使用**：Puppeteer会消耗更多内存，建议限制并发请求
3. **启动时间**：首次启动浏览器可能需要几秒钟
4. **安全性**：在生产环境中需要适当的安全配置

## 故障排除

### Chrome未找到
- 确保系统已安装Chrome浏览器
- 设置环境变量 `CHROME_PATH` 指向Chrome可执行文件

### 内存不足
- 增加服务器内存
- 限制并发PDF生成请求
- 优化HTML内容大小

### 渲染超时
- 增加 `timeout` 设置
- 检查HTML内容是否包含无法加载的外部资源

## 后续优化

1. **缓存机制**：对相同内容的PDF进行缓存
2. **队列处理**：使用队列管理大量PDF生成请求
3. **Docker支持**：创建包含Chrome的Docker镜像
4. **性能监控**：添加PDF生成性能指标
5. **批量处理**：支持一次性处理多个文件集合
