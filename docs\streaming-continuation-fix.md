# 流式续写问题修复文档

## 问题描述

当模型超过 maxToken 进行续写时，存在两个主要问题：

### 问题一：续写内容直接显示在对话面板
- 续写的内容没有被正确处理，而是直接显示在对话面板中
- 应该被 `processMessageContent` 函数处理成文件摘要，但实际上显示了原始内容

### 问题二：续写的文件没有被正确提取和预览
- 续写生成的文件（如 `slide-4-architecture.html`、`slide-5-capabilities.html`）没有被正确提取
- 这些文件没有显示在右侧预览区域

## 修复方案

### 1. 添加续写内容标记机制

在 `app/api/chat-stream/route.ts` 中：
- 为续写内容添加特殊标记 `[CONTINUE_CONTENT]`
- 这样客户端可以识别哪些内容是续写的

### 2. 改进流式处理器

在 `lib/streaming/content-stream-processor.ts` 中：
- 检测续写内容标记
- 正确处理续写内容的合并

### 3. 优化对话显示逻辑

在 `app/content-generator/content-generator-stream.tsx` 中：
- 检查是否包含续写标记
- 如果是续写内容，不更新流式显示内容，只更新完整内容用于最终处理

### 4. 改进消息内容处理

在 `app/content-generator/components/conversation-panel.tsx` 中：
- 在 `processMessageContent` 函数中清理续写标记
- 确保续写内容也能正确转换为文件摘要

### 5. 增强文件提取机制

在 `app/lib/code-extractor.ts` 中：
- 在文件提取前清理续写标记
- 确保续写后的完整内容能正确提取所有文件

## 技术实现细节

### 续写标记处理
```typescript
// 检查是否包含续写标记
const isContinueContent = delta.includes('[CONTINUE_CONTENT]');

// 如果是续写内容，不更新流式显示内容
if (!isContinueContent) {
  setStreamingContent(fullContent);
}
```

### 消息内容清理
```typescript
// 清理续写标记
let cleanContent = content.replace(/\[CONTINUE_CONTENT\]/g, '');
```

### 文件提取优化
```typescript
// 清理续写标记
const cleanMessage = message.replace(/\[CONTINUE_CONTENT\]/g, '');
```

## 预期效果

修复后应该实现：

1. **对话面板正确显示**：
   - 第一次生成的文件正确显示为摘要
   - 续写的文件也正确显示为摘要
   - 不会在对话面板中显示原始文件内容

2. **文件正确提取**：
   - 所有文件（包括续写生成的）都能被正确提取
   - 文件能正确显示在右侧预览区域

3. **流式体验优化**：
   - 续写过程中不会干扰用户界面
   - 最终结果完整且正确

## 测试验证

可以通过以下方式验证修复效果：

1. 发送一个会生成多个文件的请求（如"写一个介绍大模型的PPT"）
2. 观察是否所有文件都正确显示为摘要
3. 检查右侧预览区域是否显示所有文件
4. 验证续写过程中的用户体验

## 相关文件

- `app/api/chat-stream/route.ts` - 流式API路由
- `lib/streaming/content-stream-processor.ts` - 流式处理器
- `app/content-generator/content-generator-stream.tsx` - 主要流式组件
- `app/content-generator/components/conversation-panel.tsx` - 对话面板组件
- `app/lib/code-extractor.ts` - 文件提取逻辑
