import React from 'react';

/**
 * 内容生成器流式处理器
 * 专门为content-generator设计的流式内容处理工具
 */

export interface StreamEvent {
  type: 'content' | 'file' | 'task' | 'error' | 'finish';
  data: any;
  timestamp: number;
}

export interface ContentStreamOptions {
  onContent?: (delta: string, fullContent: string) => void;
  onFileDetected?: (fileInfo: { type: 'html' | 'markdown', content: string }) => void;
  onTaskDetected?: (tasks: any[]) => void;
  onError?: (error: Error) => void;
  onFinish?: (finalContent: string) => void;
}

export class ContentStreamProcessor {
  private fullContent = '';
  private decoder = new TextDecoder();
  private options: ContentStreamOptions;
  private isProcessing = false;
  private abortController?: AbortController;

  constructor(options: ContentStreamOptions = {}) {
    this.options = options;
  }

  /**
   * 处理流式响应
   */
  async processStream(response: Response): Promise<void> {
    if (this.isProcessing) {
      throw new Error('已有流正在处理中');
    }

    this.isProcessing = true;
    this.fullContent = '';
    this.abortController = new AbortController();

    try {
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流读取器');
      }

      while (true) {
        const { done, value } = await reader.read();

        if (done || this.abortController.signal.aborted) {
          break;
        }

        // 解码数据块
        const chunk = this.decoder.decode(value, { stream: true });

        // 检查是否是续写内容标记
        if (chunk.includes('[CONTINUE_CONTENT]')) {
          // 处理续写内容
          const continueContent = chunk.replace(/\[CONTINUE_CONTENT\]/g, '');
          this.fullContent += continueContent;

          // 触发内容更新回调，标记为续写内容
          this.options.onContent?.(continueContent, this.fullContent);
        } else {
          this.fullContent += chunk;
          // 触发内容更新回调
          this.options.onContent?.(chunk, this.fullContent);
        }

        // 检测文件内容
        this.detectFiles();

        // 检测任务内容
        this.detectTasks();
      }

      // 流处理完成
      this.options.onFinish?.(this.fullContent);
    } catch (error) {
      console.error('流处理错误:', error);
      this.options.onError?.(error as Error);
    } finally {
      this.isProcessing = false;
      this.abortController = undefined;
    }
  }

  /**
   * 检测文件内容
   */
  private detectFiles(): void {
    // 检测HTML内容
    if (this.fullContent.includes('```html') ||
        this.fullContent.includes('<!DOCTYPE html') ||
        this.fullContent.includes('<html')) {
      this.options.onFileDetected?.({
        type: 'html',
        content: this.fullContent
      });
    }

    // 检测Markdown内容
    if (this.fullContent.includes('```markdown') ||
        this.fullContent.includes('```md') ||
        this.fullContent.includes('# ')) {
      this.options.onFileDetected?.({
        type: 'markdown',
        content: this.fullContent
      });
    }
  }

  /**
   * 检测任务内容
   */
  private detectTasks(): void {
    // 简单的任务检测逻辑
    const taskPatterns = [
      /任务\s*\d+[:：]/g,
      /步骤\s*\d+[:：]/g,
      /\d+\.\s*[^\n]+/g
    ];

    for (const pattern of taskPatterns) {
      const matches = this.fullContent.match(pattern);
      if (matches && matches.length > 1) {
        // 检测到多个任务项，触发任务检测回调
        this.options.onTaskDetected?.(matches);
        break;
      }
    }
  }

  /**
   * 停止流处理
   */
  abort(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
  }

  /**
   * 获取当前完整内容
   */
  getFullContent(): string {
    return this.fullContent;
  }

  /**
   * 检查是否正在处理
   */
  isStreamProcessing(): boolean {
    return this.isProcessing;
  }
}

/**
 * 创建内容流处理器实例
 */
export function createContentStreamProcessor(options: ContentStreamOptions = {}): ContentStreamProcessor {
  return new ContentStreamProcessor(options);
}

/**
 * 流式内容处理钩子
 */
export function useContentStreamProcessor(options: ContentStreamOptions = {}) {
  const processorRef = React.useRef<ContentStreamProcessor | null>(null);

  React.useEffect(() => {
    processorRef.current = createContentStreamProcessor(options);

    return () => {
      processorRef.current?.abort();
    };
  }, []);

  const processStream = React.useCallback(async (response: Response) => {
    if (processorRef.current) {
      await processorRef.current.processStream(response);
    }
  }, []);

  const abort = React.useCallback(() => {
    processorRef.current?.abort();
  }, []);

  return {
    processStream,
    abort,
    isProcessing: processorRef.current?.isStreamProcessing() ?? false,
    getFullContent: () => processorRef.current?.getFullContent() ?? ''
  };
}
