{"name": "llamacoder-new", "version": "0.1.0", "private": true, "scripts": {"postinstall": "prisma generate", "dev": "next dev -p 4080 -H 0.0.0.0", "build": "prisma generate && prisma migrate deploy && next build", "start": "next start -p 4080", "lint": "next lint && tsc --noEmit --noUnusedLocals", "test": "vitest"}, "dependencies": {"@ai-sdk/openai": "^1.3.20", "@codesandbox/sandpack-react": "^2.19.10", "@codesandbox/sandpack-themes": "^2.0.21", "@codesandbox/sdk": "^0.3.0", "@conform-to/zod": "^1.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@langchain/anthropic": "^0.3.20", "@langchain/community": "^0.3.42", "@langchain/openai": "^0.5.10", "@neondatabase/serverless": "^0.10.4", "@next/bundle-analyzer": "^15.1.5", "@prisma/adapter-neon": "^6", "@prisma/client": "^6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@tailwindcss/typography": "^0.5.15", "@vercel/og": "^0.6.4", "ai": "^4.3.10", "class-variance-authority": "^0.7.1", "dedent": "^1.5.3", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "grapesjs": "^0.22.7", "grapesjs-preset-webpage": "^1.0.3", "html2pdf.js": "^0.10.3", "jszip": "^3.10.1", "langchain": "^0.3.24", "lucide-react": "^0.468.0", "next": "^15.3.2", "next-client-cookies": "^2.0.1", "next-plausible": "^3.12.4", "next-s3-upload": "^0.3.4", "node-html-parser": "^7.0.1", "openai": "^4.90.0", "pdf-lib": "^1.17.1", "puppeteer-core": "^24.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.11", "remark-gfm": "^4.0.1", "shiki": "^1.27.2", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "together-ai": "^0.11.1", "use-stick-to-bottom": "^1.0.43", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "15.1.4", "jsdom": "^27.0.0-beta.0", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.10", "prisma": "^6.2.1", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^3.1.4"}}